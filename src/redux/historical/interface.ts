import { StatusEnum } from '../types'

export interface IHistoricalImpactStatus {
  getKeyAchievementStatus: StatusEnum
  getHistoricalImpactStatus: StatusEnum
  addHistoricalImpactStatus: StatusEnum
  deleteHistoricalImpactStatus: StatusEnum
  updateHistoricalImpactStatus: StatusEnum
  HistoricalImpactSortStatus: StatusEnum
  keyAchievements: any[]
  keyAchievement: any
  formData: any
}

export interface IGetHistoricalImpactResponse {
  data: IHistoricalImpact[]
  message: string
  success: boolean
}

export interface IHistoricalImpact {
  id?: number
  project_summary_id?: string
  sort_description?: string
  description?: string
  project_to_phase_ids?: string
  impact_on_works?: string
  last_updated?: string
  updated_by?: string
}

export interface IHistoricalImpactsStatus {
  getHistoricalImpactStatus: StatusEnum
  addHistoricalImpactStatus: StatusEnum
  deleteHistoricalImpactStatus: StatusEnum
  updateHistoricalImpactStatus: StatusEnum
  HistoricalImpactSortStatus: StatusEnum
  HistoricalImpacts: IHistoricalImpact[]
  HistoricalImpact: IHistoricalImpact
  formData: any
}
