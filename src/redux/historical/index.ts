import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { IGetHistoricalImpactResponse, IHistoricalImpact, IHistoricalImpactsStatus } from './interface'
import { StatusEnum } from '../types'
import { ApiDeleteNoAuth, ApiGetNoAuth, ApiPostNoAuth, ApiPutNoAuth } from '@/src/api'

const initialState: IHistoricalImpactsStatus = {
  getHistoricalImpactStatus: StatusEnum.Idle,
  addHistoricalImpactStatus: StatusEnum.Idle,
  deleteHistoricalImpactStatus: StatusEnum.Idle,
  updateHistoricalImpactStatus: StatusEnum.Idle,
  HistoricalImpactSortStatus: StatusEnum.Idle,
  HistoricalImpacts: [],
  HistoricalImpact: {},
  formData: {}
}

export const getHistoricalImpacts = createAsyncThunk(
  '/get-historical',
  async (data: { period: string; project_name?: string }, thunkAPI) => {
    const period = data.period
    try {
      const response = await ApiGetNoAuth(
        `/historical-delays?period=${period}${data?.project_name ? `&project_name=${encodeURIComponent(data?.project_name)}` : ''}`,
      )
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const addHistoricalImpacts = createAsyncThunk('/add-historical', async (data: IHistoricalImpact, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/historical-delays`, data)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})
export const HistoricalImpactSort = createAsyncThunk('/historical-delays/bulk-sort', async (data: any, thunkAPI) => {
  try {
    const response = await ApiPostNoAuth(`/historical-delays/bulk-sort`, data)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const updateHistoricalImpacts = createAsyncThunk(
  '/update-historical',
  async (data: { id?: number; data: IHistoricalImpact }, thunkAPI) => {
    try {
      const response = await ApiPutNoAuth(`/historical-delays/${data.id}`, data.data)
      if (response) {
        return thunkAPI.fulfillWithValue(response)
      }
    } catch (error: any) {
      return thunkAPI.rejectWithValue(error)
    }
  },
)

export const deleteHistoricalImpacts = createAsyncThunk('/delete-historical', async (data: number, thunkAPI) => {
  try {
    const response = await ApiDeleteNoAuth(`/historical-delays/${data}`)
    if (response) {
      return thunkAPI.fulfillWithValue(response)
    }
  } catch (error: any) {
    return thunkAPI.rejectWithValue(error)
  }
})

export const HistoricalImpactSlice = createSlice({
  name: 'HistoricalImpact',
  initialState,
  reducers: {
    //set selected rows
    updateFormData(state, action) {
      state.formData = action.payload
    },
  },
  extraReducers: (builder) => {
    //getHistoricalImpacts
    builder.addCase(getHistoricalImpacts.pending, (state) => {
      state.getHistoricalImpactStatus = StatusEnum.Pending
    })
    builder.addCase(getHistoricalImpacts.fulfilled, (state, action) => {
      state.getHistoricalImpactStatus = StatusEnum.Success
      const actionPayload = action.payload as IGetHistoricalImpactResponse
      state.HistoricalImpacts = actionPayload.data
    })
    builder.addCase(getHistoricalImpacts.rejected, (state) => {
      state.getHistoricalImpactStatus = StatusEnum.Failed
    })
    //HistoricalImpactSort
    builder.addCase(HistoricalImpactSort.pending, (state) => {
      state.HistoricalImpactSortStatus = StatusEnum.Pending
    })
    builder.addCase(HistoricalImpactSort.fulfilled, (state, action) => {
      state.HistoricalImpactSortStatus = StatusEnum.Success
    })
    builder.addCase(HistoricalImpactSort.rejected, (state) => {
      state.HistoricalImpactSortStatus = StatusEnum.Failed
    })
    //addHistoricalImpacts
    builder.addCase(addHistoricalImpacts.pending, (state) => {
      state.addHistoricalImpactStatus = StatusEnum.Pending
    })
    builder.addCase(addHistoricalImpacts.fulfilled, (state, action) => {
      state.addHistoricalImpactStatus = StatusEnum.Success
    })
    builder.addCase(addHistoricalImpacts.rejected, (state) => {
      state.addHistoricalImpactStatus = StatusEnum.Failed
    })
    //updateHistoricalImpacts
    builder.addCase(updateHistoricalImpacts.pending, (state) => {
      state.updateHistoricalImpactStatus = StatusEnum.Pending
    })
    builder.addCase(updateHistoricalImpacts.fulfilled, (state, action) => {
      state.updateHistoricalImpactStatus = StatusEnum.Success
    })
    builder.addCase(updateHistoricalImpacts.rejected, (state) => {
      state.updateHistoricalImpactStatus = StatusEnum.Failed
    })
    //deleteHistoricalImpacts
    builder.addCase(deleteHistoricalImpacts.pending, (state) => {
      state.deleteHistoricalImpactStatus = StatusEnum.Pending
    })
    builder.addCase(deleteHistoricalImpacts.fulfilled, (state, action) => {
      state.deleteHistoricalImpactStatus = StatusEnum.Success
    })
    builder.addCase(deleteHistoricalImpacts.rejected, (state) => {
      state.deleteHistoricalImpactStatus = StatusEnum.Failed
    })
  },
})

export const { updateFormData } = HistoricalImpactSlice.actions

// Export the reducer
export default HistoricalImpactSlice.reducer
