import { useEffect, useState } from 'react'
import { useFormik } from 'formik'
import { useRouter } from 'next/router'
import { showCustomToast } from '../toast/ToastManager'
import { permissionList, Routes } from '@/src/constant/enum'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import useRole from '@/src/redux/role/useRole'

const filterAvailableItems = (itemList: string[], availableList: string[]): string[] => {
  const uniqueItems: string[] = []

  itemList.forEach((item) => {
    if (availableList.includes(item) && !uniqueItems.includes(item)) {
      uniqueItems.push(item)
    }
  })

  return uniqueItems
}

export const useRoleForm = () => {
  const {
    addRoleApi,
    roles,
    getRoleApi,
    updateRoleApi,
    getViewPermissionsApi,
    getProjectAndEntityApi,
    projectAndEntities,
    permissions,
  } = useRole()
  const { currentPeriod } = useMasterPeriod()
  const router = useRouter()

  const { id } = router.query
  const [success, setSuccess] = useState(false)
  const [editRole, setEditRole] = useState<any>(null)
  const [selectedPermissionType, setSelectedPermissionType] = useState<string>('')
  const [loading, setLoading] = useState<boolean>(false) // Add loading state
  const keyAchievementExcludedPermission = ['Key Achievements Sorting']

  const handleSubmit = async (values: any) => {
    setLoading(true) // Start loading

    if (id) {
      let permission = [...values.permissions]

      const res: any = await updateRoleApi({
        id: id,
        role_name: values.role_name,
        view_permissions: filterAvailableItems(permission, permissionList),
        permission_type: selectedPermissionType,
        owning_entities: values.selectedEntities,
        projects: values.selectedProjects,
      })

      if (res.payload.success === true) {
        getRoleApi()
        setSuccess(true)
        formik.resetForm()
        showCustomToast('Record successfully updated', 'success')
      }
    } else {
      const res: any = await addRoleApi({
        role_name: values.role_name,
        view_permissions: values.permissions,
        permission_type: selectedPermissionType,
        owning_entities: values.selectedEntities,
        projects: values.selectedProjects,
      })

      if (res.payload.success === true) {
        getRoleApi()
        setSuccess(true)
        formik.resetForm()
        router.push(`${Routes.USER_MANAGEMENT}`)
      }
    }
    setLoading(false) // End loading
  }

  const formik = useFormik({
    initialValues: {
      role_name: '',
      permissions: [] as string[],
      permission_type: [] as string[],
      selectedEntities: [] as string[],
      selectedProjects: [] as string[],
    },
    enableReinitialize: true,
    onSubmit: (values) => {
      handleSubmit(values)
    },
  })

  useEffect(() => {
    const fetchRoles = async () => {
      setLoading(true) // Start loading
      try {
        await getRoleApi() // Fetch roles
      } catch (error) {
        console.error('Error fetching roles:', error)
        // Optionally show an error toast or handle the error
      } finally {
        setLoading(false) // End loading
      }
    }

    fetchRoles() // Call the async function
  }, []) // Dependency array, including `getRoleApi` if it's a function

  useEffect(() => {
    const currentRole = roles.find((item: any) => item.id == id)
    if (currentRole) {
      setEditRole(currentRole)
      formik.setValues({
        role_name: currentRole?.role_name || '',
        permissions: currentRole?.view_permissions || [],
        permission_type: currentRole.permission_type ? [currentRole.permission_type] : [],
        selectedEntities: currentRole?.owning_entities || [],
        selectedProjects: currentRole.projects || [],
      })
    }
  }, [id, roles, formik.setValues])

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true) // Start loading
      try {
        // Fetch view permissions
        await getViewPermissionsApi()

        // Fetch project and entity data if currentPeriod exists
        if (currentPeriod) {
          await getProjectAndEntityApi(currentPeriod)
        }
      } catch (error) {
        console.error('Error fetching data:', error)
        // Optionally show an error toast or handle the error
      } finally {
        setLoading(false) // End loading
      }
    }

    fetchData()
  }, [currentPeriod])

  useEffect(() => {
    if (formik.values.permission_type.includes('entity')) {
      setSelectedPermissionType('entity')
    } else if (formik.values.permission_type.includes('projects')) {
      setSelectedPermissionType('projects')
    } else {
      setSelectedPermissionType('')
    }
  }, [formik.values.permission_type])

  const handlePermissionChange = (permission: string) => {
    const isSelected = formik.values.permissions.includes(permission)

    let newPermissions = [...formik.values.permissions]

    const addPermissions = (perms: string[]) => {
      newPermissions = [...newPermissions, ...perms.filter((p) => !newPermissions.includes(p))]
    }

    const removePermissions = (perms: string[]) => {
      newPermissions = newPermissions.filter((p) => !perms.includes(p))
    }

    if (permission === 'Summary') {
      const summarySections = permissions.filter((section: any) => section.startsWith('Summary'))
      if (isSelected) {
        removePermissions([...summarySections, 'Governance'])
      } else {
        addPermissions([...summarySections, 'Governance'])
      }
    } else if (permission === 'Status') {
      const statusSections = permissions.filter((section: any) => section.startsWith('Status'))
      if (isSelected) {
        removePermissions(statusSections)
      } else {
        addPermissions(statusSections)
      }
    } else if (permission === 'Media') {
      const statusSections = permissions.filter((section: any) => section.startsWith('Media'))
      if (isSelected) {
        removePermissions(statusSections)
      } else {
        addPermissions(statusSections)
      }
    } else if (permission === 'Commercials') {
      const statusSections = permissions.filter((section: any) => section.startsWith('Commercials'))
      if (isSelected) {
        removePermissions(statusSections)
      } else {
        addPermissions(statusSections)
      }
    } else if (permission.startsWith('Governance')) {
      if (isSelected) {
        const summarySections = formik.values.permissions
          .filter((section: any) => section.startsWith('Summary'))
          .filter((item) => {
            return item !== permission
          })
          .filter((item) => item !== 'Summary')
        if (summarySections.length == 0) {
          removePermissions(['Summary'])
        }
        removePermissions([permission])
      } else {
        addPermissions([permission, 'Summary'])
      }
    } else if (permission.startsWith('Summary') && permission !== 'Summary') {
      if (isSelected) {
        const summarySections = formik.values.permissions
          .filter((section: any) => section.startsWith('Summary'))
          .filter((item) => {
            return item !== permission
          })
          .filter((item) => item !== 'Summary')
        if (summarySections.length == 0 && !formik.values.permissions.includes('Governance')) {
          removePermissions(['Summary'])
        }
        removePermissions([permission])
      } else {
        addPermissions([permission, 'Summary'])
      }
    } else if (permission.startsWith('Status') && permission !== 'Status') {
      if (isSelected) {
        const statusSection = formik.values.permissions
          .filter((section: any) => section.startsWith('Status'))
          .filter((item) => {
            return item !== permission
          })
          .filter((item) => item !== 'Status')
        if (statusSection.length == 0) {
          removePermissions(['Status'])
        }
        removePermissions([permission])
      } else {
        addPermissions([permission, 'Status'])
      }
    } else if (permission.startsWith('Media') && permission !== 'Media') {
      if (isSelected) {
        const statusSection = formik.values.permissions
          .filter((section: any) => section.startsWith('Media'))
          .filter((item) => {
            return item !== permission
          })
          .filter((item) => item !== 'Media')
        if (statusSection.length == 0) {
          removePermissions(['Media'])
        }
        removePermissions([permission])
      } else {
        addPermissions([permission, 'Media'])
      }
    } else if (permission.startsWith('Commercials') && permission !== 'Commercials') {
      if (isSelected) {
        const statusSection = formik.values.permissions
          .filter((section: any) => section.startsWith('Commercials'))
          .filter((item) => {
            return item !== permission
          })
          .filter((item) => item !== 'Commercials')
        if (statusSection.length == 0) {
          removePermissions(['Commercials'])
        }
        removePermissions([permission])
      } else {
        addPermissions([permission, 'Commercials'])
      }
    } else if (
      permission.startsWith('Key Achievement & Risk') &&
      permission !== 'Key Achievement' &&
      !keyAchievementExcludedPermission.includes(permission)
    ) {
      if (isSelected) {
        const statusSection = formik.values.permissions
          .filter((section: any) => section.startsWith('Key Achievement & Risk'))
          .filter((item) => {
            return item !== permission
          })
          .filter((item) => item !== 'Key Achievement & Risk')
        if (statusSection.length == 0) {
          removePermissions(['Key Achievement'])
        }
        removePermissions([permission])
      } else {
        addPermissions([permission, 'Key Achievement & Risk'])
      }
    } else {
      if (isSelected) {
        removePermissions([permission])
      } else {
        addPermissions([permission])
      }
    }
    formik.setValues({ ...formik.values, permissions: newPermissions })
  }

  return {
    loading,
    formik,
    editRole,
    permissions,
    projectAndEntities,
    selectedPermissionType,
    setSelectedPermissionType,
    navigateToUserManagement: () => router.push(`${Routes.USER_MANAGEMENT}`),
    handlePermissionChange: (permission: string) => {
      handlePermissionChange(permission)
    },
    handlePermissionTypeChange: (permissionType: string) => {
      const isChecked = formik.values.permission_type.includes(permissionType)
      const newPermissionTypes = isChecked ? [] : [permissionType]
      formik.setValues({
        ...formik.values,
        permission_type: newPermissionTypes,
      })
    },
  }
}
