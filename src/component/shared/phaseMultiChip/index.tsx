import React from 'react'
import { Box, Chip, Stack, Typography } from '@mui/material'
import styles from './PhaseMultiChip.module.scss'

interface Item {
  tooltip: string
  [key: string]: any
}

interface PhaseMultiChipProps {
  currentPhase: Item
}

const PhaseMultiChip: React.FC<PhaseMultiChipProps> = ({ currentPhase }) => {
  return (
    <>
      {currentPhase?.tooltip && (
        <div className={styles.phaseChipContainer}>
          <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
            {currentPhase?.tooltip?.split('#').map((label, index) => (
              <Chip
                className={styles.phaseChip}
                key={index + `${label}`}
                label={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <Typography variant="body2" className={styles.phaseChipText}>
                      {label}
                    </Typography>
                  </Box>
                }
                variant="outlined"
                sx={{
                  backgroundColor: 'white',
                  borderColor: '#e0e0e0',
                  color: '#666666',
                  height: 'auto',
                  py: 0.5,
                  px: 1,
                  '& .MuiChip-label': {
                    fontWeight: 400,
                    px: 1,
                    py: 0.5,
                    color: '#666666',
                  },
                  '& .MuiChip-deleteIcon': {
                    marginLeft: 1,
                    marginRight: -0.5,
                  },
                  '&:hover': {
                    backgroundColor: '#f8f9fa',
                    borderColor: '#d0d0d0',
                  },
                }}
              />
            ))}
          </Stack>
        </div>
      )}
    </>
  )
}

export default PhaseMultiChip
