import { useState } from 'react'
import { WarningAmberOutlined } from '@mui/icons-material'
import { toast } from 'sonner'
import styles from './KeyRiskTable.module.scss'
import DragCell from '@/src/component/customCells/dragCell'
import LineClampWithTooltip from '@/src/component/shared/lineClampWithTooltip'
import PulseModel from '@/src/component/shared/pulseModel'
import TanStackTable from '@/src/component/shared/tanStackTable'
import { singleAssociationValueSorting } from '@/src/component/shared/tanStackTable/helper'
import { CustomColumnDef } from '@/src/component/shared/tanStackTable/interface'
import DeleteIcon from '@/src/component/svgImages/deleteIcon'
import EditIcon from '@/src/component/svgImages/editIcon'
import {
  fetchKeyAchievements,
  getKeyAchievementsTableData,
  reorderKeyAchievements,
} from '@/src/component/updateProgress/progressForm/keyAchievements/helper'
import { KeyAchievementValidation } from '@/src/component/updateProgress/progressForm/keyAchievements/validation'
import ValidationModel from '@/src/component/updateProgress/progressForm/validationModel'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useKeyAchievement from '@/src/redux/keyAchievement/useKeyAchievement'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { IProjects } from '@/src/services/projects/interface'
import { errorToast } from '@/src/utils/toastUtils'

export interface IKeyRiskTableProps {
  edit: boolean
  isEditForUser: boolean
  project: IProjects
  expanded: boolean
  gridFilters: { colId: string; values: any }[]
  setGridFilters: (args: { colId: string; values: any }[]) => void
  setEditKeyRiskIndex: (index: number | null) => void
  setIsOpenDrawer: (isOpen: boolean) => void
}

const KeyRiskTable: React.FC<IKeyRiskTableProps> = ({
  edit,
  isEditForUser,
  project,
  expanded,
  gridFilters,
  setGridFilters,
  setEditKeyRiskIndex,
  setIsOpenDrawer,
}) => {
  const [isModelOpen, setIsModelOpen] = useState(false)
  const [deleteModel, setDeleteModel] = useState<number | null>(null)
  const [isValidationModel, setIsValidationModel] = useState(false)
  const [validationMessage, setValidationMessage] = useState<string[]>([])
  const {
    keyAchievements,
    getKeyAchievementsApi,
    deleteKeyAchievementsApi,
    updateKeyAchievementsApi,
    keyAchievementSortApi,
  } = useKeyAchievement()
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const { currentUser } = useAuthorization()
  const keyAchievement: any = keyAchievements.filter((item) => item.project_name === project?.project_name)

  const onCellUpdate = async (cell: any, newValue: any, row: any): Promise<boolean> => {
    if (!row) return false

    try {
      const { id, ...rowData } = row
      const payload = {
        ...rowData,
        [cell.columnId]: newValue,
        last_updated: new Date(),
      }

      const isStageStatusValidDate = await KeyAchievementValidation.StageStatus(payload, true)
      if (isStageStatusValidDate) {
        setIsValidationModel(true)
        setValidationMessage(['Stage status must be filled for this record.'])
        return true
      }

      const isSubStageStatusValidDate = await KeyAchievementValidation.SubStageStatus(payload, true)
      if (isSubStageStatusValidDate) {
        setIsValidationModel(true)
        setValidationMessage(['Sub Stage status must be filled for this record.'])
        return true
      }

      delete payload.updated_by
      delete payload.LookupProjectToPhase
      delete payload.MasterProjectStageStatus
      delete payload.MasterProjectSubStage
      delete payload.sub_stage
      delete payload.stage_status

      const response: Record<string, any> = await updateKeyAchievementsApi({ id, data: payload })

      if (!response?.payload?.success) {
        errorToast(response?.payload?.response?.data?.message)
        return false
      }

      const getResponse = await fetchKeyAchievements(getKeyAchievementsApi, currentPeriod, project?.project_name)

      if (!getResponse?.payload?.success) {
        errorToast(getResponse?.payload?.response?.data?.message)
        return false
      }

      return true
    } catch (error) {
      console.error('Error while updating key achievement cell:', error)
      errorToast('Unexpected error occurred')
      return false
    }
  }

  const columns: CustomColumnDef<any>[] = [
    {
      accessorKey: 'dragCol',
      header: '',
      cell: ({ row }) => <DragCell rowId={row?.id} />,
      size: 40,
      align: 'left',
    },
    {
      accessorKey: 'actionCol',
      header: 'Action',
      cell: ({ row }: { row: any }) => (
        <div className={styles.actionButtons}>
          {isEditForUser && (
            <EditIcon
              className={styles.editRowIcon}
              onClick={() => {
                if (!isEditForUser) {
                  toast(`The current reporting period is locked`, {
                    icon: <WarningAmberOutlined />,
                  })
                } else {
                  setIsOpenDrawer(true)
                  setEditKeyRiskIndex(row.id)
                }
              }}
            />
          )}
          {isEditForUser && (
            <DeleteIcon
              className={styles.editRowIcon}
              onClick={() => {
                if (!isEditForUser) {
                  toast(`The current reporting period is locked`, {
                    icon: <WarningAmberOutlined />,
                  })
                } else {
                  setDeleteModel(row.id)
                }
              }}
            />
          )}
        </div>
      ),
      size: 70,
    },
    { accessorKey: 'key_achievement_sorting_order', header: 'ID', size: 90, visible: false },
    {
      accessorKey: 'phase',
      header: 'Phase/Package',
      childAccessorKey: ['LookupProjectToPhase'],
      filterType: 'wildcard-single-association',
      size: 150,
      require: false,
      sortingFn: (...data) => singleAssociationValueSorting(...data, 'Single phase'),
      cell: ({ row }: { row: any }) => {
        return <div>{row.original?.LookupProjectToPhase?.phase || '-'}</div>
      },
    },
    {
      accessorKey: 'stage_status',
      header: 'Stage Status',
      filterType: 'projectStatus',
      size: 150,
      require: false,
    },
    {
      accessorKey: 'sub_stage',
      header: 'Sub Stage',
      size: 150,
      require: false,
    },
    {
      accessorKey: 'key_achievements_last_period',
      header: 'Key Achievements (Last Period)',
      flex: 5,
      isEditableCell: edit,
      editableType: 'textArea',
      cell: ({ row }) => {
        const val = row.original.key_achievements_last_period
        return <LineClampWithTooltip lineNumber={2}>{val}</LineClampWithTooltip>
      },
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
      require: true,
    },
    {
      accessorKey: 'plans_next_period',
      header: 'Plans (Next Period)',
      flex: 5,
      isEditableCell: edit,
      editableType: 'textArea',
      // editableType: 'richTextEditor',
      // cell: ({ row }: { row: any }) => {
      //   return <RichTextEditor value={row?.original?.next_plan_delim} isEdit={false} isGrid={true} />
      // },
      cell: ({ row }) => {
        const val = row.original.plans_next_period
        return <LineClampWithTooltip lineNumber={2}>{val}</LineClampWithTooltip>
      },
      onEditCell: (cell, newValue, row) => {
        return onCellUpdate(cell, newValue, row)
      },
      require: true,
    },
  ]

  const handleDragAndDrop = async (data: any, dragId: string, dropId: string) => {
    if (!currentUser?.role?.view_permissions?.includes('Key Achievements Sorting')) return
    const dragItem = keyAchievements.find((item: any) => item.id.toString() === dragId.toString())
    const dropItem = keyAchievements.find((item: any) => item.id.toString() === dropId.toString())
    if (!dragItem || !dropItem) return

    const updatedData = reorderKeyAchievements(keyAchievements, dragItem, dropItem)

    const newPayload = updatedData.map((item: any) => ({
      id: item.id.toString(),
      key_achievement_sorting_order: Number(item.key_achievement_sorting_order),
    }))

    const res: any = await keyAchievementSortApi({ period: currentPeriod, keyAchievements: newPayload })
    // const res: any = await keyAchievementSortApi({ period: currentPeriod, keyAchievementsPlans: newPayload })

    if (res?.payload.success) {
      await fetchKeyAchievements(getKeyAchievementsApi, currentPeriod, project?.project_name)
    }
  }

  return (
    <>
      <div className={`${styles.tableContainer} ${!expanded && styles.expandTable}`}>
        <TanStackTable
          rows={getKeyAchievementsTableData(keyAchievement)}
          columns={columns}
          onDragEnd={handleDragAndDrop}
          gridFilters={gridFilters}
          setGridFilters={setGridFilters}
          className="KeyRiskTableWrapper"
          stickyColumnCount={5} // Adjust sticky columns for government table
        />
      </div>
      <PulseModel
        closable={false}
        style={{ width: 'fitContent' }}
        open={isValidationModel}
        onClose={() => setIsValidationModel(false)}
        content={<ValidationModel messages={validationMessage} onClose={() => setIsValidationModel(false)} />}
      />
    </>
  )
}

export default KeyRiskTable
