import { useMemo } from 'react'
import {
  predefinedProjectStatusOrder,
  reorderStageStatusArray,
} from '@/src/component/customCells/headerCell/generateOptions'
import { generateMultiSelectOptionsFromPhases } from '@/src/component/updateProgress/helper'
import { IStatus } from '@/src/redux/status/interface'
import {
  populateDropdownOptions,
  prepareDropdownOptionsFromObject,
  sortArrayByKeyWithTypeConversion,
  getUniqueArrayByKey,
  setMultiSelectedValueToWildCard,
} from '@/src/utils/arrayUtils'
import { getStageStatusByPermission } from '@/src/utils/statusTab/stageStatusByPermission'

const getPhaseStageAssociations = (records: any[]) => {
  const phaseStageMap = new Map()

  records.forEach((record: any) => {
    const phaseArray = record.phases // now an array of objects
    const stageStatusValue = record.stage_status

    const subStageValue =
      typeof record.sub_stage?.project_sub_stage === 'string' && record.sub_stage?.project_sub_stage.trim() !== ''
        ? record.sub_stage
        : null

    // Skip invalid stage_status
    if (
      !stageStatusValue ||
      typeof stageStatusValue?.project_stage_status !== 'string' ||
      stageStatusValue?.project_stage_status.trim() === ''
    ) {
      return
    }

    const stageInfo = {
      stageStatus: stageStatusValue,
      subStage: subStageValue,
    }

    const stageInfoString = JSON.stringify(stageInfo)

    // Process phase array of objects
    if (Array.isArray(phaseArray) && phaseArray.length > 0) {
      phaseArray.forEach((phaseObj: any) => {
        // if (!phaseObj?.phase) return // skip if missing phase name

        const phaseKey = JSON.stringify(phaseObj) // use entire object as key

        if (!phaseStageMap.has(phaseKey)) {
          phaseStageMap.set(phaseKey, new Set())
        }

        phaseStageMap.get(phaseKey).add(stageInfoString)
      })
    }
  })

  // Convert Map to desired result
  const result: Array<{ phases: any; stages: any[] }> = []
  for (const [phaseKey, stagesSet] of phaseStageMap.entries()) {
    result.push({
      phases: JSON.parse(phaseKey),
      stages: Array.from(stagesSet).map((s: any) => JSON.parse(s)),
    })
  }

  return result
}

export const useDropdownOption = (statuses: IStatus[], phaseIds: number[], currentUser: any) => {
  return useMemo(() => {
    const statusesByPermission = statuses?.filter((item: IStatus) =>
      getStageStatusByPermission(currentUser.role).includes(item.stage_status),
    )

    const sortedStatusesByPermission = statusesByPermission?.length
      ? sortArrayByKeyWithTypeConversion(
          statusesByPermission?.map((status) => ({
            ...status,
            project_status_sorting_order: Number(status.project_status_sorting_order),
          })),
          'project_status_sorting_order',
          true,
        )
      : []

    const phaseWithStageStatuses = sortedStatusesByPermission.map((item: any) => {
      return {
        phases: item.LookupProjectToPhase,
        stage_status: item.MasterProjectStageStatus,
        sub_stage: item.MasterProjectSubStage,
      }
    })

    const phaseStageAssociations: any = getPhaseStageAssociations(phaseWithStageStatuses)

    const uniquePhases =
      phaseStageAssociations?.length > 0
        ? phaseStageAssociations?.map((item: any) => {
            return item?.phases
          })
        : []

    const phaseOptions =
      uniquePhases?.length > 0
        ? uniquePhases
            ?.map((phase: any) => {
              return {
                label: phase?.phase,
                value: phase?.id,
              }
            })
            ?.filter((item: any) => item.value !== null && item.label !== null)
        : []

    //* Get record by selected phasedIds
    const matchingRecordByPhase = phaseIds
      ? phaseIds?.map((item: any) => {
          return phaseStageAssociations.find((record: any) => record?.phases?.id === item)
        })
      : []

    //* Filter all stage from found record
    const stageStatusOptions = matchingRecordByPhase
      .map((item: any) => {
        return item?.stages?.map((stage: any) => {
          return {
            label: stage?.stageStatus?.project_stage_status,
            value: stage?.stageStatus?.id,
          }
        })
      })
      .flat()
      ?.filter((item: any) => !!item.value && !!item.label)

    //* Filter all subStage from found record
    const subStageStatusOptions = matchingRecordByPhase
      .map((item: any) => {
        return item?.stages?.map((stage: any) => {
          return {
            label: stage?.subStage?.project_sub_stage,
            value: stage?.subStage?.id,
          }
        })
      })
      .flat()
      ?.filter((item: any) => !!item.value && !!item.label)

    //* Make unique and re-order in predefined order
    const uniqueStageStatuses = getUniqueArrayByKey(stageStatusOptions, 'value')
    const sortedUniqueStageStatuses = reorderStageStatusArray(uniqueStageStatuses, predefinedProjectStatusOrder)

    const uniqueSubStageStatuses = getUniqueArrayByKey(subStageStatusOptions, 'value')

    return {
      phaseOptions: phaseOptions,
      stageStatusOptions: sortedUniqueStageStatuses,
      subStageOptions: uniqueSubStageStatuses,
    }
  }, [statuses, phaseIds, currentUser?.role])
}
