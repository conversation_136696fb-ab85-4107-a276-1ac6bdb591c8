import React, { useEffect, useMemo, useState } from 'react'
import { useFormik } from 'formik'
import { useRouter } from 'next/router'
import styles from './AddKeyAchievementDrawer.module.scss'
import { useDropdownOption } from './useDropdownOption'
import Button from '@/src/component/shared/button'
import ComboBox from '@/src/component/shared/combobox'
import CustomComboBox from '@/src/component/shared/combobox/combobox'
import MultiSearchAutoSelect from '@/src/component/shared/multiAutoSelect/multiAutoSelect'
import PhaseMultiChip from '@/src/component/shared/phaseMultiChip'
import Textarea from '@/src/component/shared/textArea'
import CheckIcon from '@/src/component/svgImages/checkIcon'
import SaveIcon from '@/src/component/svgImages/saveIcon'
import { createPostAndUpdateKeyAchievementPayload } from '@/src/component/updateProgress/progressForm/keyAchievements/helper'
import { StageStatus, SubStage } from '@/src/redux/areaOfConcern/interface'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import { IKeyAchievement } from '@/src/redux/keyAchievement/interface'
import useKeyAchievement from '@/src/redux/keyAchievement/useKeyAchievement'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import { IStatus } from '@/src/redux/status/interface'
import useStatus from '@/src/redux/status/useStatus'
import { IProjects } from '@/src/services/projects/interface'
import {
  getMultiValues,
  getUniqueArrayByKey,
  getValue,
  setMultiSelectedValueToWildCard,
  sortArrayByKeyWithTypeConversion,
} from '@/src/utils/arrayUtils'
import { getStageStatusByPermission } from '@/src/utils/statusTab/stageStatusByPermission'
import { errorToast, successToast } from '@/src/utils/toastUtils'

interface AddCommercialProps {
  onClose: () => void
  editKeyAchievementIndex: number | null
  keyAchievement: IKeyAchievement[]
  project?: IProjects
  statuses?: IStatus[]
}

const AddKeyAchievementDrawer: React.FC<AddCommercialProps> = ({
  onClose,
  editKeyAchievementIndex,
  keyAchievement,
  project,
  statuses,
}) => {
  //react-hook
  const [disable, setDisable] = useState(false)
  const [isStatusChanged, setIsStatusChanged] = useState(false)
  //custom-hook
  const { currentUser } = useAuthorization()
  const { keyAchievements, getKeyAchievementsApi, addKeyAchievementsApi, updateKeyAchievementsApi } =
    useKeyAchievement()
  const { currentPeriod } = useMasterPeriod()

  const router = useRouter()

  const keyAchievementData: any = useMemo(() => {
    return keyAchievements.find((item: any) => item.id === editKeyAchievementIndex)
  }, [keyAchievements, editKeyAchievementIndex])

  const initialValues = useMemo(() => {
    return {
      key_achievements_last_period: (editKeyAchievementIndex && keyAchievementData.key_achievements_last_period) || '',
      plans_next_period: (editKeyAchievementIndex && keyAchievementData.plans_next_period) || '',
      lookup_project_to_phase_id: (editKeyAchievementIndex && keyAchievementData?.LookupProjectToPhase?.id) || '',
      stage_status:
        (editKeyAchievementIndex && keyAchievementData?.MasterProjectStageStatus?.project_stage_status) || '',
      sub_stage: (editKeyAchievementIndex && keyAchievementData?.MasterProjectSubStage?.project_sub_stage) || '',
      master_project_stage_status_id:
        (editKeyAchievementIndex && keyAchievementData.master_project_stage_status_id) || 0,
      master_project_sub_stage_id: (editKeyAchievementIndex && keyAchievementData.master_project_sub_stage_id) || 0,
    }
  }, [editKeyAchievementIndex, keyAchievementData])

  useEffect(() => {
    formik.resetForm({ values: initialValues })
  }, [initialValues])

  const onSubmit = async (values: any) => {
    setDisable(true)
    try {
      const todayDate = new Date()
      const payload: IKeyAchievement = createPostAndUpdateKeyAchievementPayload(
        values,
        project?.project_name,
        todayDate,
        null,
        currentPeriod,
      )

      let response: Record<string, any>

      if (editKeyAchievementIndex) {
        response = await updateKeyAchievementsApi({
          id: editKeyAchievementIndex,
          data: payload,
        })
      } else {
        const keyAchievementSort = keyAchievement
          ?.slice()
          .sort((a: any, b: any) => a.key_achievement_sorting_order - b.key_achievement_sorting_order)

        const sort = keyAchievementSort.length
          ? Number(keyAchievementSort[keyAchievementSort.length - 1].key_achievement_sorting_order) + 1
          : 1
        const newPayload: IKeyAchievement = createPostAndUpdateKeyAchievementPayload(
          values,
          project?.project_name,
          todayDate,
          sort,
          currentPeriod,
        )
        response = await addKeyAchievementsApi(newPayload)
      }

      if (response?.payload?.success) {
        if (currentPeriod) {
          await getKeyAchievementsApi({ period: currentPeriod })
        }
        formik.resetForm()
        onClose && onClose()
        successToast(response?.payload?.message || 'Key Achievement added successfully')
      } else {
        errorToast(response?.payload?.response?.data?.message || 'An unexpected error occurred. Please try again.')
      }
    } catch (error) {
      console.error('Error occurred:', error)
      errorToast('An unexpected error occurred. Please try again.')
    } finally {
      setDisable(false)
    }
  }

  const formik: any = useFormik({
    initialValues,
    enableReinitialize: true,
    onSubmit,
  })

  const handleFieldChange = (val: any, keyName: string) => {
    formik.setFieldValue(keyName, val)
  }

  const { phaseOptions, stageStatusOptions, subStageOptions } = useDropdownOption(
    statuses || [],
    formik.values.lookup_project_to_phase_id,
    currentUser,
  )

  const phaseMultiSelectOptions = useMemo(() => {
    return phaseOptions?.map((item: any) => ({
      id: item.value,
      name: item.label,
    }))
  }, [phaseOptions])

  return (
    <div className={styles.container}>
      <form className={styles.form} onSubmit={formik.handleSubmit}>
        <div className={styles.header}>
          <div className={styles.headerTitle}>
            {editKeyAchievementIndex ? 'Edit Key Achievement' : 'Add Key Achievement'}
          </div>
          <div className={styles.actionButtons}>
            <Button
              type="submit"
              disabled={
                disable ||
                !formik.dirty ||
                formik.isSubmitting ||
                !formik.values.key_achievements_last_period ||
                !formik.values.plans_next_period ||
                !formik.values.master_project_stage_status_id ||
                !formik.values.lookup_project_to_phase_id?.length
              }
              startIcon={editKeyAchievementIndex ? <CheckIcon /> : <SaveIcon />}
            >
              {editKeyAchievementIndex ? 'Update' : 'Save'}
            </Button>
            <Button
              className={styles.closeButton}
              color="secondary"
              onClick={() => {
                formik.resetForm()
                onClose()
              }}
            >
              X Close
            </Button>
          </div>
        </div>
        <div className={styles.content}>
          <div className={styles.dropDownContainer}>
            {/* <CustomComboBox
              options={phaseOptions}
              labelText={'Phase/Package *'}
              placeholder="Type of search..."
              className={styles.comboBoxInput}
              value={
                formik.values.lookup_project_to_phase_id
                  ? getMultiValues(phaseOptions, formik.values.lookup_project_to_phase_id)
                  : null
              }
              clearIcon={true}
              onChange={(val) => {
                const value = val?.value ? (Array.isArray(val?.value) ? val.value : [val.value]) : null
                if (!value) {
                  formik.setFieldValue('master_project_stage_status_id', null)
                }
                formik.setFieldValue('lookup_project_to_phase_id', value)
              }}
            /> */}
            <MultiSearchAutoSelect
              labelText="Phase/Package *'"
              placeholder={formik.values.lookup_project_to_phase_id?.length > 0 ? '' : 'Type of search...'}
              isSubOption={false}
              isSx={false}
              clearIcon={true}
              options={phaseMultiSelectOptions}
              value={formik.values.lookup_project_to_phase_id}
              handleSelectedOption={(val) => {
                console.log('val: ', val)
                if (val && val?.length === 0) {
                  formik.setFieldValue('master_project_stage_status_id', null)
                  formik.setFieldValue('master_project_sub_stage_id', null)
                }
                handleFieldChange(val, 'lookup_project_to_phase_id')
              }}
            />
            <ComboBox
              options={stageStatusOptions}
              labelText={'Stage Status *'}
              placeholder="Type of search..."
              // disabled={editKeyAchievementIndex && editKeyAchievementIndex > 0 ? true : false}
              value={
                formik.values?.master_project_stage_status_id
                  ? !!getValue(stageStatusOptions, formik.values?.master_project_stage_status_id)
                    ? getValue(stageStatusOptions, formik.values?.master_project_stage_status_id)
                    : { label: formik.values?.stage_status, value: formik.values?.master_project_stage_status_id }
                  : null
              }
              clearIcon={true}
              onChange={(val) => {
                formik.setValues({
                  ...formik.values,
                  master_project_stage_status_id: val?.value || null,
                  master_project_sub_stage_id: null,
                })
              }}
              isCustomSorting={true}
            />
          </div>
          {(getValue(stageStatusOptions, formik.values?.master_project_stage_status_id)?.label ||
            formik.values?.stage_status) === 'Design' && (
            <ComboBox
              options={subStageOptions}
              labelText={'Sub Stage'}
              // disabled={editKeyAchievementIndex && editKeyAchievementIndex > 0 ? true : false}
              placeholder="Type of search..."
              value={
                formik.values?.master_project_sub_stage_id
                  ? !!getValue(subStageOptions, formik.values?.master_project_sub_stage_id)
                    ? getValue(subStageOptions, formik.values?.master_project_sub_stage_id)
                    : { label: formik.values?.sub_stage, value: formik.values?.master_project_sub_stage_id }
                  : null
              }
              clearIcon={true}
              onChange={(val) => {
                formik.setValues({
                  ...formik.values,
                  master_project_sub_stage_id: val?.value || '',
                })
              }}
              isCustomSorting={true}
            />
          )}
          <div>
            <Textarea
              name="key_achievements_last_period"
              placeholder="Type Something..."
              labelText={'Key  Achievements (Last Period) *'}
              onChange={formik.handleChange}
              value={formik.values.key_achievements_last_period}
              onBlur={formik.handleBlur}
            />
            <Textarea
              name="plans_next_period"
              placeholder="Type Something..."
              labelText={'Plans (Next Period) *'}
              onChange={formik.handleChange}
              value={formik.values?.plans_next_period}
              onBlur={formik.handleBlur}
            />
          </div>
        </div>
      </form>
    </div>
  )
}

export default AddKeyAchievementDrawer
