import { useEffect, useMemo, useState } from 'react'
import { RestartAlt, WarningAmberOutlined } from '@mui/icons-material'
import AddOutlinedIcon from '@mui/icons-material/AddOutlined'
import EditNoteIcon from '@mui/icons-material/EditNote'
import { Button } from '@mui/material'
import { useQuery } from '@tanstack/react-query'
import Image from 'next/image'
import { useRouter } from 'next/router'
import { toast } from 'sonner'
import AddKeyAchievementDrawer from './addKeyAchievementDrawer.tsx'
import styles from './KeyAchievementTab.module.scss'
import KeyAchievementTable from './keyAchievementTable'
import KeyAchievementButton from './keyAchievementTable/keyAchievementButton'
import PulseButton from '../../shared/button/pulseButton'
import Drawer from '../../shared/drawer'
import Loader from '../../shared/loader'
import TypographyField from '../../shared/typography'
import AddRoundedIcon from '../../svgImages/addRounded'
import { PROJECT_QUERY_KEY } from '@/src/hooks/useProjects'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useKeyAchievement from '@/src/redux/keyAchievement/useKeyAchievement'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import useStatus from '@/src/redux/status/useStatus'
import { getMasterOneProject } from '@/src/services/projects'
import { canEditUser } from '@/src/utils/userUtils'

const KeyAchievementTab = ({
  selectedTab,
  expanded,
  gridFilters,
  setGridFilters,
  isOpenDrawer,
  setIsOpenDrawer,
}: {
  selectedTab: string
  expanded: boolean
  gridFilters: { colId: string; values: any }[]
  setGridFilters: (args: { colId: string; values: any }[]) => void
  isOpenDrawer: boolean
  setIsOpenDrawer: any
}) => {
  const router = useRouter()
  const { currentPeriod, freezeType, mainPeriod } = useMasterPeriod()
  const { currentUser } = useAuthorization()
  const { statuses, getStatusApi } = useStatus()
  const { keyAchievements, getKeyAchievementsApi } = useKeyAchievement()
  const [isDrawerOpen, setIsDrawerOpen] = useState(false)
  const [editKeyAchievementIndex, setEditKeyAchievementIndex] = useState<number | null>(null)
  const [isEditTable, setIsEditTable] = useState(false)

  const [loader, setLoader] = useState(true)

  const getProjectPayload = {
    projectName: router?.query?.slug as string,
    period: currentPeriod,
  }

  const {
    data: project,
    refetch,
    isLoading,
  } = useQuery({
    queryKey: [PROJECT_QUERY_KEY, router?.query?.slug, currentPeriod],
    queryFn: () => getMasterOneProject(getProjectPayload),
    select: (response) => response.data,
    enabled: false,
  })

  // Memoized Edit Permissions
  const isEditForUser = useMemo(
    () => canEditUser(currentUser, freezeType, currentPeriod, mainPeriod),
    [currentUser, freezeType, currentPeriod, mainPeriod],
  )

  useEffect(() => {
    refetch()
  }, [])

  // Fetch Data
  const fetchData = async () => {
    try {
      await Promise.all([
        getStatusApi({ period: currentPeriod, project_name: router.query.slug?.toString() }),
        getKeyAchievementsApi({ period: currentPeriod, project_name: router.query.slug?.toString() }),
      ])
      setLoader(false)
    } catch (error) {
      console.error('Error fetching status data:', error)
    }
  }

  useEffect(() => {
    if (selectedTab === 'Key Achievements') {
      if (!isDrawerOpen) {
        setLoader(true)
        fetchData()
      }
    }
  }, [selectedTab, isDrawerOpen])

  const handleCloseDrawer = () => {
    setIsOpenDrawer(false)
    setEditKeyAchievementIndex(null)
  }

  const hasFilter = useMemo(
    () => gridFilters?.some((item: { colId: string; values: any }) => item.values.length > 0),
    [gridFilters],
  )

  return (
    <div className={styles.mainContainer}>
      {loader ? (
        <Loader />
      ) : (
        <>
          {keyAchievements?.length ? (
            <div className={styles.tableWrapper}>
              <div className={styles.button}>
                <PulseButton
                  onClick={() => {
                    if (!isEditForUser) {
                      toast(`The current reporting period is locked`, {
                        icon: <WarningAmberOutlined />,
                      })
                    } else {
                      setIsOpenDrawer(true)
                    }
                  }}
                  label="Add Key Achievements"
                  icon={<AddOutlinedIcon fontSize="large" />}
                  disabled={loader}
                />
                <div className={styles.rightCorner}>
                  {hasFilter ? (
                    <Button endIcon={<RestartAlt />} variant="contained" onClick={() => setGridFilters([])}>
                      Reset Filter
                    </Button>
                  ) : null}

                  <Button
                    variant={isEditTable ? 'outlined' : 'contained'}
                    startIcon={<EditNoteIcon />}
                    onClick={() => {
                      if (!isEditForUser) {
                        toast(`The current reporting period is locked`, {
                          icon: <WarningAmberOutlined />,
                        })
                      } else {
                        setIsEditTable(!isEditTable)
                      }
                    }}
                    disabled={loader || !isEditForUser}
                  >
                    Edit Table
                  </Button>
                </div>
              </div>
              <KeyAchievementTable
                edit={isEditTable}
                isEditForUser={isEditForUser}
                project={project}
                expanded={expanded}
                gridFilters={gridFilters}
                setGridFilters={setGridFilters}
                setEditKeyAchievementIndex={setEditKeyAchievementIndex}
                setIsOpenDrawer={setIsOpenDrawer}
              />
            </div>
          ) : (
            <div className={styles.container}>
              <div className={styles.contentWrapper}>
                <Image src="/svg/contracts.svg" alt="contracts" width={200} height={200} />
                <TypographyField
                  variant={'bodyBold'}
                  className={styles.noRecordsText}
                  text="Looks like there are no Key Achievements records added!"
                />
                <KeyAchievementButton
                  setIsOpenDrawer={setIsOpenDrawer}
                  isEditForUser={isEditForUser}
                  startIcon={<AddRoundedIcon />}
                />
              </div>
            </div>
          )}
        </>
      )}
      <Drawer anchor="right" open={isOpenDrawer} onClose={handleCloseDrawer}>
        <AddKeyAchievementDrawer
          onClose={handleCloseDrawer}
          editKeyAchievementIndex={editKeyAchievementIndex}
          keyAchievement={keyAchievements}
          project={project}
          statuses={statuses}
        />
      </Drawer>
    </div>
  )
}

export default KeyAchievementTab
