@import '/styles/color.scss';
@import '/styles/breakpoints.scss';

.tabsContainer {
  width: 100%;
  overflow: auto;
  padding: 15px 10px;
  &::-webkit-scrollbar {
    width: 0rem;
    height: 0rem;
  }
  &::-webkit-scrollbar-track {
    border-radius: 0.3125rem;
  }
  &::-webkit-scrollbar-thumb {
    background: transparent;
    border-radius: 0.3125rem;
  }
}

.tabsData {
  background: #ffffff;
  flex: 1 1 auto;
  height: calc(100vh - 294px);
  padding: 10px ;
  &::-webkit-scrollbar {
    width: 4px;
  }
  &::-webkit-scrollbar-track {  
    border-radius: 5px;
  }
  &::-webkit-scrollbar-thumb {
    background: #ddd;
    border-radius: 5px;
  }
  @include respond-to('mobile') {
    margin-left: 20px;
  }

  @include respond-to('laptop') {
    margin-left: 0px;
  }
}

.collapseHeight {
  height: calc(100vh - 303px);
}
.isNotCollapseHeight {
  height: calc(100vh - 171px);
}

.tabLayoutContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.tabButtons {
  color: $WHITE;
  display: flex;
  gap: 0.5rem;
  padding-right: 1rem;
}
