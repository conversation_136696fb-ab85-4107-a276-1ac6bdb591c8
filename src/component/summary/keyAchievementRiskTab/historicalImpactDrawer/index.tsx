import React, { useMemo, useState } from 'react'
import { useFormik } from 'formik'
import styles from './HistoricalImpactDrawer.module.scss'
import { useDropdownOption } from '../../keyAchievementTab/addKeyAchievementDrawer.tsx/useDropdownOption'
import Button from '@/src/component/shared/button'
import MultiSearchAutoSelect from '@/src/component/shared/multiAutoSelect/multiAutoSelect'
import Textarea from '@/src/component/shared/textArea'
import TextInputField from '@/src/component/shared/textInputField'
import CheckIcon from '@/src/component/svgImages/checkIcon'
import SaveIcon from '@/src/component/svgImages/saveIcon'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import { IStatus } from '@/src/redux/status/interface'
import { IProjects } from '@/src/services/projects/interface'

interface HistoricalImpactDrawerProps {
  onClose: () => void
  editIndex: number | null
  data: any
  project?: IProjects
  statuses?: IStatus[]
}
const HistoricalImpactDrawer: React.FC<HistoricalImpactDrawerProps> = ({ onClose, editIndex, data, statuses }) => {
  const [isLoading, setIsLoading] = useState(false)

  const initialValues = useMemo(() => {
    return {
      lookup_project_to_phase_id: (editIndex && data?.LookupProjectToPhase?.id) || [],
      sort_description: editIndex ? data?.sort_description : '',
      description: editIndex ? data?.sort_description : '',
      impected_works: editIndex ? data?.sort_description : '',
    }
  }, [editIndex, data])

  const onSubmit = async (values: any) => {
    // TODO: Submit logic here
    setIsLoading(true)
    console.log('values: ', values)
    setIsLoading(false)
  }

  const formik = useFormik({
    initialValues,
    enableReinitialize: true,
    onSubmit,
  })

  const handleChange = (key: string, value: any) => {
    formik.setFieldValue(key, value)
  }

  const { currentUser } = useAuthorization()

  const { phaseOptions } = useDropdownOption(statuses || [], formik.values.lookup_project_to_phase_id, currentUser)

  const phaseMultiSelectOptions = useMemo(() => {
    return phaseOptions?.map((item: any) => ({
      id: item.value,
      name: item.label,
    }))
  }, [phaseOptions])

  return (
    <div className={styles.container}>
      <form className={styles.form} onSubmit={formik.handleSubmit}>
        <div className={styles.header}>
          <div className={styles.headerTitle}>{editIndex ? 'Edit Historical Impact' : 'Add Historical Impact'}</div>
          <div className={styles.actionButtons}>
            <Button
              type="submit"
              disabled={
                isLoading ||
                !formik.dirty ||
                formik.isSubmitting ||
                !formik.values.lookup_project_to_phase_id?.length ||
                !formik.values.sort_description ||
                !formik.values.impected_works ||
                !formik.values.description
              }
              startIcon={editIndex ? <CheckIcon /> : <SaveIcon />}
            >
              {editIndex ? 'Update' : 'Save'}
            </Button>
            <Button
              className={styles.closeButton}
              color="secondary"
              onClick={() => {
                formik.resetForm()
                onClose()
              }}
            >
              X Close
            </Button>
          </div>
        </div>
        <div className={styles.content}>
          <div className={styles.dropDownContainer}>
            <MultiSearchAutoSelect
              labelText="Phase"
              placeholder="Type of search..."
              isSubOption={false}
              isSx={false}
              clearIcon={true}
              options={phaseMultiSelectOptions}
              value={formik.values.lookup_project_to_phase_id}
              handleSelectedOption={(val) => handleChange('lookup_project_to_phase_id', val)}
            />
          </div>
          <TextInputField
            name="sort_description"
            placeholder="Type Something..."
            labelText="Item"
            onChange={(e) => handleChange('sort_description', e.target.value)}
            value={formik.values.sort_description}
            variant={'outlined'}
          />
          <TextInputField
            name="impected_works"
            placeholder="Type Something..."
            labelText="Impacted Works"
            onChange={(e) => handleChange('impected_works', e.target.value)}
            value={formik.values.impected_works}
            variant={'outlined'}
          />
          <Textarea
            name="description"
            placeholder="Type Something..."
            labelText="Description"
            onChange={(e) => handleChange('description', e.target.value)}
            value={formik.values.description}
          />
        </div>
      </form>
    </div>
  )
}

export default HistoricalImpactDrawer
