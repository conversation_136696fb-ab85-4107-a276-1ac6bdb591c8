
@import '/styles/color.scss';

.container {
  width: 100%;
  min-width: 650px;
  max-width: 650px;
  .header {
    border-bottom: 1px solid $LIGHT_200;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 20px;
    .headerTitle {
      font-family: Poppins;
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;
      letter-spacing: -0.04em;
      text-align: left;
      padding: 13px 0 13px 20px;
      color: $BLACK;
    }
    .actionButtons {
      display: flex;
      gap: 20px;

      .closeButton {
        padding: 8px 10px;
      }
    }
  }
  .content {
    margin: 0px 20px 0px 20px;
    height: calc(100vh - 100px);
    overflow: scroll;
    .addProjectButton {
      padding: 8px 10px;
    }
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
}

.dropDownContainer {
  display: flex;
  gap: 20px;
}

.form {
  display: flex;
  gap: 20px;
  flex-direction: column;
}

// .textArea {
//   :focus {
//     background: $FOCUS_TEXTFIELD_BG !important;
//     border: 1px solid $FOCUS_TEXTFIELD_BORDER !important;
//   }
// }
.endAdornment {
  margin-right: -9px !important;
}

.richTextEditor {
  margin-bottom: 10px;
  > div {
    background: #fafafa;
    border: none;
  }
}
