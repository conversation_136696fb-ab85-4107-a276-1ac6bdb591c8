import React, { useCallback, useState } from 'react'
import HistoricalImpactTab from './historicalImpactTab'
import styles from './KeyAchievementRiskTab.module.scss'
import KeyAchievementTab from '../keyAchievementTab'
import Tab from '../tab'
import { RatingTab } from '@/src/redux/dataEntryScreen/interface'

interface KeyAchievementRiskTabProps {
  selectedTab: RatingTab
  expanded: boolean
  gridFilters: { colId: string; values: any }[]
  setGridFilters: (args: { colId: string; values: any }[]) => void
  isOpenDrawer: boolean
  setIsOpenDrawer: any
}

const KeyAchievementRiskTab: React.FC<KeyAchievementRiskTabProps> = ({
  selectedTab,
  expanded,
  gridFilters,
  setGridFilters,
  isOpenDrawer,
  setIsOpenDrawer,
}) => {
  const [selectedSubTab, setSelectedSubTab] = useState('Key Achievements')
  const [tabDataHeight, setTabDataHeight] = useState<number | undefined>(undefined)

  // Calculate available height for tabData based on banner height
  const updateTabDataHeight = useCallback(() => {
    const banner = document.getElementById('project-banner')
    if (banner) {
      const bannerRect = banner.getBoundingClientRect()
      const windowHeight = window.innerHeight
      const extraHeight = 40
      setTabDataHeight(windowHeight - bannerRect.height - extraHeight)
    }
  }, [])

  const tabComponents = [
    {
      name: 'Key Achievements',
      component: (
        <KeyAchievementTab
          selectedTab={selectedSubTab}
          expanded={expanded}
          // TODO : set different state for Key Achievement
          gridFilters={gridFilters}
          setGridFilters={setGridFilters}
          isOpenDrawer={isOpenDrawer}
          setIsOpenDrawer={setIsOpenDrawer}
        />
      ),
    },
    {
      name: 'Key Risks',
      component: <div>KeyRiskTab</div>,
    },
    {
      name: 'Historical Impact',
      component: (
        <HistoricalImpactTab
          selectedTab={selectedSubTab}
          expanded={expanded}
          // TODO : set different state for Key Achievement
          gridFilters={[]}
          setGridFilters={() => {}}
          isOpenDrawer={isOpenDrawer}
          setIsOpenDrawer={setIsOpenDrawer}
        />
      ),
    },
  ]

  const handleTabSelect = useCallback((tabName: string) => setSelectedSubTab(tabName), [setSelectedSubTab])

  return (
    <div
      className={styles.tabsContainer}
      // style={{ padding: 20, marginBottom: 20 }}
    >
      <div className={`${styles.tabLayoutContainer} `}>
        <div className={styles.tabButtons}>
          {tabComponents.map((tab) => (
            <Tab key={tab.name} name={tab.name} onSelect={handleTabSelect} isActive={selectedTab === tab.name}>
              {tab.name}
            </Tab>
          ))}
        </div>
      </div>
      <div className={styles.tabsData} style={tabDataHeight ? { height: tabDataHeight } : {}}>
        {tabComponents.map((tab) => {
          if (tab.name === selectedSubTab) {
            return (
              <React.Fragment key={tab.name}>
                {/* <Toaster position="bottom-right" /> */}
                {tab.component}
              </React.Fragment>
            )
          }
          return null
        })}
      </div>
    </div>
  )
}

export default KeyAchievementRiskTab
