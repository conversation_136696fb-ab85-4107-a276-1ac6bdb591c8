import React, { useEffect, useState, useCallback } from 'react'
import { useQuery } from '@tanstack/react-query'
import dynamic from 'next/dynamic'
import Head from 'next/head'
import { useRouter } from 'next/router'
import { Toaster } from 'sonner'
import styles from './Summary.module.scss'
import Loader from '@/src/component/shared/loader'
import Banner from '@/src/component/summary/banner'
import HandOver from '@/src/component/summary/handover'
import { tabPermissions } from '@/src/component/summary/summaryTab/constant'
import { PROJECT_QUERY_KEY } from '@/src/hooks/useProjects'
import useAuthorization from '@/src/redux/authorization/useAuthorization'
import useDataEntryScreen from '@/src/redux/dataEntryScreen/useDataEntryScreen'
import useGovernance from '@/src/redux/governance/useGovernance'
import useMasterPeriod from '@/src/redux/masterPeriods/useMasterPeriod'
import useProjectSummary from '@/src/redux/projectSummary/useProjectSummary'
import useStatus from '@/src/redux/status/useStatus'
import useSustainability from '@/src/redux/sustainability/useSustainability'
import { getMasterOneProject } from '@/src/services/projects'
// Dynamic imports for lazy loading components
const AuthoritiesApproval = dynamic(() => import('@/src/component/summary/authoritiesApprovals'), {
  loading: () => <Loader />,
  ssr: false,
})
const CommercialsTab = dynamic(() => import('@/src/component/summary/commercialsTab'), {
  loading: () => <Loader />,
  ssr: false,
})
const KpisTab = dynamic(() => import('@/src/component/summary/kpisTab'), {
  loading: () => <Loader />,
  ssr: false,
})
const Media = dynamic(() => import('@/src/component/summary/media'), {
  loading: () => <Loader />,
  ssr: false,
})
const SpaTab = dynamic(() => import('@/src/component/summary/spaTab'), {
  loading: () => <Loader />,
  ssr: false,
})
const StatusTab = dynamic(() => import('@/src/component/summary/statusTab'), {
  loading: () => <Loader />,
  ssr: false,
})
const KeyAchievementRiskTab = dynamic(() => import('@/src/component/summary/keyAchievementRiskTab'), {
  loading: () => <Loader />,
  ssr: false,
})
const KeyRiskTab = dynamic(() => import('@/src/component/summary/keyRiskTab'), {
  loading: () => <Loader />,
  ssr: false,
})
const SummaryTab = dynamic(() => import('@/src/component/summary/summaryTab'), {
  loading: () => <Loader />,
  ssr: false,
})

const Summary = () => {
  const [loader, setLoader] = useState(true)
  const [statusTabGridFilters, setStatusTabGridFilters] = useState<{ colId: string; values: any }[]>([])
  const [keyAchievementTabGridFilters, setKeyAchievementTabGridFilters] = useState<{ colId: string; values: any }[]>([])
  const [keyRiskTabGridFilters, setKeyRiskTabGridFilters] = useState<{ colId: string; values: any }[]>([])
  const [spaGovernmentFilters, setSpaGovernmentFilters] = useState<{ colId: string; values: any }[]>([])
  const [spaGroupAFilters, setSpaGroupAFilters] = useState<{ colId: string; values: any }[]>([])
  const [spaGroupBFilters, setSpaGroupBFilters] = useState<{ colId: string; values: any }[]>([])
  const router = useRouter()
  const { setRecentProject } = useProjectSummary()
  const { currentUser } = useAuthorization()
  const [expanded, setExpanded] = useState(true)
  const [designManager, setDesignManager] = useState<any>()
  const [isOpenDrawer, setIsOpenDrawer] = useState(false)
  const { selectedTab, setSelectedTabApi } = useDataEntryScreen()
  const { currentPeriod } = useMasterPeriod()
  const { getStatusApi } = useStatus()
  const { getSustainabilitiesApi } = useSustainability()
  const { getGovernanceApi } = useGovernance()
  const { statuses } = useStatus()
  const getProjectPayload = {
    projectName: encodeURIComponent(router.query.slug?.toString() as string),
    period: currentPeriod,
  }
  const { data: project, refetch } = useQuery({
    queryKey: [PROJECT_QUERY_KEY],
    queryFn: () => getMasterOneProject(getProjectPayload),
    select: (response) => response.data,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
  })

  // Update handler for SPA filters to include government
  const handleSpaFilters = useCallback((type: 'A' | 'B' | 'GOV', filters: { colId: string; values: any }[]) => {
    switch (type) {
      case 'A':
        setSpaGroupAFilters(filters)
        break
      case 'B':
        setSpaGroupBFilters(filters)
        break
      case 'GOV':
        setSpaGovernmentFilters(filters)
        break
    }
  }, [])

  const tabComponents = [
    {
      name: 'Authority Approvals Tracking',
      component: <AuthoritiesApproval />,
    },
    {
      name: 'Summary',
      permission: 'Summary',
      component: <SummaryTab expanded={expanded} />,
    },
    {
      name: 'Progress',
      permission: 'Status',
      component: (
        <StatusTab
          selectedTab={selectedTab}
          expanded={expanded}
          gridFilters={statusTabGridFilters}
          setGridFilters={setStatusTabGridFilters}
        />
      ),
    },
    {
      name: 'Key Achievement & Risk',
      permission: 'Key Achievement & Risk',
      component: (
        <KeyAchievementRiskTab
          selectedTab={selectedTab}
          expanded={expanded}
          gridFilters={keyAchievementTabGridFilters}
          setGridFilters={setKeyAchievementTabGridFilters}
          isOpenDrawer={isOpenDrawer}
          setIsOpenDrawer={setIsOpenDrawer}
        />
      ),
    },
    {
      name: 'Key Risk',
      permission: 'Key Risk',
      component: (
        <KeyRiskTab
          selectedTab={selectedTab}
          expanded={expanded}
          gridFilters={keyRiskTabGridFilters}
          setGridFilters={setKeyRiskTabGridFilters}
          isOpenDrawer={isOpenDrawer}
          setIsOpenDrawer={setIsOpenDrawer}
        />
      ),
    },
    {
      name: 'Project Overview',
      permission: 'Project Overview',
      component: <KpisTab selectedTab={selectedTab} />,
    },
    {
      name: 'Commercials',
      permission: 'Commercials',
      component: <CommercialsTab selectedTab={selectedTab} />,
    },
    {
      name: 'SPA & Milestones',
      permission: 'SPA & Milestones',
      component: (
        <SpaTab
          selectedTab={selectedTab}
          isOpenDrawer={isOpenDrawer}
          setIsOpenDrawer={setIsOpenDrawer}
          expanded={expanded}
          governmentFilters={spaGovernmentFilters}
          groupAFilters={spaGroupAFilters}
          groupBFilters={spaGroupBFilters}
          setFilters={handleSpaFilters}
        />
      ),
    },
    {
      name: 'Media',
      component: <Media />,
    },
    {
      name: 'Handover',
      component: <HandOver />,
    },
  ]

  useEffect(() => {
    const hasPermission = (tab: any) => currentUser?.role?.view_permissions?.includes(tabPermissions[tab] || tab)

    if (selectedTab && hasPermission(selectedTab)) {
      setSelectedTabApi(selectedTab)
    } else {
      const defaultTab = Object.keys(tabPermissions).find((tab) => hasPermission(tab))
      if (defaultTab) {
        setSelectedTabApi(defaultTab)
      }
    }
  }, [currentUser, selectedTab])

  const fetchData = useCallback(async () => {
    try {
      if (currentPeriod) {
        // await getReasonForDelaysApi()
        // await getMitigationRecoveryPlansApi()
        // await getMasterNextStepsToAdvancedProgressesApi()
        // await getNonRecoverableDelayJustificationsApi()
        await getGovernanceApi({ period: currentPeriod, project_name: router.query.slug?.toString() as string })
        await getSustainabilitiesApi({ period: currentPeriod, project_name: router.query.slug?.toString() as string })
        await getStatusApi({ period: currentPeriod, project_name: router.query.slug?.toString() as string })
        refetch() // Refetch data when the route changes
        setRecentProject({
          projectName: router.query.slug?.toString() as string,
        })
      }
    } catch (error) {
      console.error('Error fetching data:', error)
      // } finally {
      //   setLoader(false)
    }
  }, [currentPeriod, router.query.slug])

  useEffect(() => {
    setLoader(true)
    fetchData()
  }, [currentPeriod, fetchData])

  useEffect(() => {
    if (project?.project_name !== router.query.slug) {
      setLoader(true)
    } else {
      setLoader(false)
    }
  }, [router.query.slug, project])

  useEffect(() => {
    const statusOfProject = statuses.filter((item: any) => item.project_name === router.query.slug)
    const designManagerValue = statusOfProject.map((item: any) =>
      item.DesignManagers?.map((manager: any) => manager?.design_manager),
    )
    setDesignManager(
      [...new Set(designManagerValue)]
        .filter(Boolean)
        .map((item: any) => (item?.includes(',') ? item?.split(',').join(', ') : item)),
    )
  }, [statuses])

  const [tabDataHeight, setTabDataHeight] = useState<number | undefined>(undefined)

  // Calculate available height for tabData based on banner height
  const updateTabDataHeight = useCallback(() => {
    const banner = document.getElementById('project-banner')
    if (banner) {
      const bannerRect = banner.getBoundingClientRect()
      const windowHeight = window.innerHeight
      const extraHeight = 40
      setTabDataHeight(windowHeight - bannerRect.height - extraHeight)
    }
  }, [])

  useEffect(() => {
    updateTabDataHeight()
    window.addEventListener('resize', updateTabDataHeight)
    return () => window.removeEventListener('resize', updateTabDataHeight)
  }, [expanded, updateTabDataHeight, loader])

  return (
    <>
      <Head>
        <title>Project Details | {router.query.slug}</title>
        <meta name="description" content="Project Details" />
      </Head>
      {loader ? (
        <Loader />
      ) : (
        <>
          <Banner setExpanded={setExpanded} expanded={expanded} />
          <div className={styles.tabData} style={tabDataHeight ? { height: tabDataHeight } : {}}>
            {tabComponents.map((tab) => {
              if (tab.name === selectedTab) {
                if (tab.permission && !currentUser?.role?.view_permissions?.includes(tab.permission)) {
                  return null
                }
                return (
                  <React.Fragment key={tab.name}>
                    {/* <Toaster position="bottom-right" /> */}
                    {tab.component}
                  </React.Fragment>
                )
              }
              return null
            })}
          </div>
        </>
      )}
    </>
  )
}

export default Summary
